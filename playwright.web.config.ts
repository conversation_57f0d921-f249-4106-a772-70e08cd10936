import { defineConfig, devices } from "@playwright/test";
import dotenv from "dotenv";

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
dotenv.config();

/**
 * 专用于 Web 测试的配置文件
 * 使用方式: pnpm playwright test --config=playwright.web.config.ts
 */
// 创建时间戳用于报告文件夹命名
const timestamp = new Intl.DateTimeFormat('zh-CN', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false
}).format(new Date()).replace(/[\/:]/g, '-');

export default defineConfig({
  testDir: "./e2e",
  // 只匹配 Web 测试文件
  testMatch: ["**/*.spec.ts"],
  timeout: 30 * 1000,
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: <PERSON><PERSON><PERSON>(process.env.CI),
  /* Retry on both CI and local */
  retries: process.env.CI ? 2 : 1,
  /* Configure workers for optimal performance */
  workers: process.env.CI ? 1 : undefined, // 在本地可以使用默认的worker数量
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['list'],
    ['html', {
      outputFolder: `reports/web-report-${timestamp}`,
      preserveOutput: true
    }],
    ['@midscene/web/playwright-report']
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    // baseURL: 'http://127.0.0.1:3000',
    /* Run tests in headful mode */
    headless: false,
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "retain-on-failure",
    /* Screenshot on failure */
    screenshot: "only-on-failure",
    /* Record video on failure */
    video: "retain-on-failure",
    /* Default viewport and window position */
    viewport: { width: 1920, height: 1080 },
    launchOptions: {
      args: ['--window-position=-1920,30','--window-size=1920,1080']
    },
  },
  /* Configure projects for major browsers with test groups */
  projects: [
    {
      name: "chromium",
      use: {
        ...devices["Desktop Chrome"],
      },
    },
  ],
});
