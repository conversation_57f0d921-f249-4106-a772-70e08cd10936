import { expect } from '@playwright/test';
import type { Page, Locator } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';

export class MeetingSystemPage {
  private readonly page: Page;
  private readonly ai: PlayWrightAiFixtureType['ai'];
  private readonly aiTap: PlayWrightAiFixtureType['aiTap'];
  private readonly aiAssert: PlayWrightAiFixtureType['aiAssert'];
  private readonly aiWaitFor: PlayWrightAiFixtureType['aiWaitFor'];

  constructor(page: Page, aiFixtures?: PlayWrightAiFixtureType) {
    this.page = page;
    if (aiFixtures) {
      this.ai = aiFixtures.ai;
      this.aiTap = aiFixtures.aiTap;
      this.aiAssert = aiFixtures.aiAssert;
      this.aiWaitFor = aiFixtures.aiWaitFor;
    }
  }

  /**
   * 导航到会议系统后台
   */
  async navigateToMeetingSystem() {
    if (this.aiTap) {
      // 使用AI点击导航到会议系统后台
      await this.aiTap('会议系统后台入口按钮');
      if (this.aiWaitFor) {
        await this.aiWaitFor('会议系统后台页面已加载', { timeoutMs: 5000 });
      }
    } else {
      await this.page.getByText('会议系统后台业务驱动财务、智能提供效率 前往').click();
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 验证用户按钮是否可见
   */
  async verifyUserButtonVisible() {
    if (this.aiAssert) {
      // 使用AI断言验证工作台按钮是否可见
      await this.aiAssert('工作台按钮在页面上可见');
    } else {
      await expect(this.page.getByText('工作台',{exact:true})).toBeVisible();
    }
  }

  /**
   * 导航到会议活动
   */
  async navigateToMeetingActivity() {
    if (this.aiTap) {
      // 使用AI点击导航到会议活动
      await this.aiTap('会议活动链接');
      if (this.aiWaitFor) {
        await this.aiWaitFor('会议活动页面已加载', { timeoutMs: 3000 });
      }
    } else {
      await this.page.getByRole('link', { name: '会议活动' }).click();
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 导航到资源管理
   */
  async navigateToResourceManagement() {
    if (this.aiTap) {
      // 使用AI点击导航到资源管理
      await this.aiTap('资源管理菜单项');
      if (this.aiWaitFor) {
        await this.aiWaitFor('资源管理页面已加载', { timeoutMs: 3000 });
      }
    } else {
      await this.page.getByRole('menuitem', { name: '资源管理' }).locator('span').click();
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 导航到订单中心
   */
  async navigateToOrderCenter() {
    if (this.aiTap) {
      // 使用AI点击导航到订单中心
      await this.aiTap('订单中心菜单项');
      if (this.aiWaitFor) {
        await this.aiWaitFor('订单中心页面已加载', { timeoutMs: 3000 });
      }
    } else {
      await this.page.getByRole('menuitem', { name: '订单中心' }).locator('span').click();
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 导航到促销管理
   */
  async navigateToPromotionManagement() {
    if (this.aiTap) {
      // 使用AI点击导航到促销管理
      await this.aiTap('促销管理菜单项');
      if (this.aiWaitFor) {
        await this.aiWaitFor('促销管理页面已加载', { timeoutMs: 3000 });
      }
    } else {
      await this.page.getByRole('menuitem', { name: '促销管理' }).locator('span').click();
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 导航到设置中心
   */
  async navigateToSettingCenter() {
    if (this.aiTap) {
      // 使用AI点击导航到设置中心
      await this.aiTap('设置中心菜单项');
      if (this.aiWaitFor) {
        await this.aiWaitFor('设置中心页面已加载', { timeoutMs: 3000 });
      }
    } else {
      await this.page.getByRole('menuitem', { name: '设置中心' }).locator('span').click();
      await this.page.waitForLoadState('networkidle');
    }
  }
}