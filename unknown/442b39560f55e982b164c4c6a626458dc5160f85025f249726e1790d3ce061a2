/**
 * 企业微信 AI 工作台自动化测试脚本
 * 
 * 该脚本自动执行以下测试流程：
 * 1. 打开企业微信并登录
 * 2. 进入 AI 工作台
 * 3. 测试 AI 工作台基本功能
 * 4. 进入并测试"扣子"智能体各项功能
 * 5. 返回创新社区
 */

import { test, expect } from "./fixture";
import { WecomAIWorkspaceTester } from './agents/wecom-ai-workspace-tester';
import { KouziAgentTester } from './agents/kouzi-agent-tester';
import { log } from './utils/common';
import { setupExitHandlers, shouldExit, cleanupAndExit } from './utils/exit-handler';

// 设置退出处理程序
setupExitHandlers();

test.describe('企业微信AI工作台自动化测试', () => {
  test('测试企业微信AI工作台基本功能', async ({ agent }) => {
    log('开始企业微信AI工作台自动化测试...', 'info');
    
    try {
      // 创建测试代理实例
      const wecomTester = new WecomAIWorkspaceTester(agent);
      
      // 执行企业微信AI工作台测试流程
      await wecomTester.openWecomAndNavigateToWorkbench();
      
      // 验证成功进入工作台
      const onWorkbench = await agent.aiQuery('当前是否在工作台页面');
      expect(onWorkbench).toBeTruthy();
      
      await wecomTester.openAIWorkspace();
      await wecomTester.testAIWorkspaceBasicFeatures();
      await wecomTester.navigateToInnovationCommunity();
      await wecomTester.openKouziAgent();
      
      log('企业微信AI工作台基本功能测试完成', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      log(`测试执行过程中发生错误: ${errorMessage}`, 'error');
      console.error(error);
      
      // 在测试框架中，使用fail来标记测试失败
      test.fail(true, `测试执行失败: ${errorMessage}`);
    }
  });
  
  test('测试"扣子"智能体', async ({ agent }) => {
    log('开始"扣子"智能体自动化测试...', 'info');
    
    try {
      // 创建测试代理实例
      const kouziTester = new KouziAgentTester(agent);
      
      // 执行"扣子"智能体测试流程
      await kouziTester.runFullTest();
      
      // 返回到创新社区
      await kouziTester.returnToCommunity();
      
      log('"扣子"智能体测试完成', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      log(`测试执行过程中发生错误: ${errorMessage}`, 'error');
      console.error(error);
      
      // 在测试框架中，使用fail来标记测试失败
      test.fail(true, `测试执行失败: ${errorMessage}`);
    }
  });
});