/**
 * Android设备管理器
 * 
 * 负责Android设备的初始化、连接和资源清理
 */

import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import { log } from './common';

export class DeviceManager {
  private device: AndroidDevice | null = null;
  private agent: AndroidAgent | null = null;

  /**
   * 初始化并连接设备
   * @param aiActionContext AI操作上下文描述
   * @returns AndroidAgent实例
   */
  async initAndConnect(aiActionContext: string = ''): Promise<AndroidAgent> {
    log('正在连接Android设备...', 'info');
    
    // 获取连接的设备
    const devices = await getConnectedDevices();
    if (devices.length === 0) {
      throw new Error('未检测到任何连接的安卓设备');
    }
    
    // 使用第一个设备
    const deviceId = devices[0].udid;
    log(`已检测到设备: ${deviceId}`, 'info');
    
    this.device = new AndroidDevice(deviceId);
    
    // 创建AI代理
    const defaultContext = '如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，等待扫码。如果出现更新提示，选择稍后或取消。';
    const context = aiActionContext || defaultContext;
    
    this.agent = new AndroidAgent(this.device, { aiActionContext: context });
    
    // 连接设备
    await this.device.connect();
    log(`设备连接成功: ${deviceId}`, 'success');
    
    return this.agent;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    if (this.device) {
      try {
        log('正在释放设备资源...', 'info');
        await this.device.destroy();
        log('设备资源已释放', 'success');
      } catch (error) {
        log(`释放设备资源时出错: ${error}`, 'error');
      } finally {
        this.device = null;
        this.agent = null;
      }
    }
  }

  /**
   * 获取当前连接的代理
   * @returns 当前AndroidAgent实例或null
   */
  getAgent(): AndroidAgent | null {
    return this.agent;
  }

  /**
   * 获取当前连接的设备
   * @returns 当前AndroidDevice实例或null
   */
  getDevice(): AndroidDevice | null {
    return this.device;
  }
}