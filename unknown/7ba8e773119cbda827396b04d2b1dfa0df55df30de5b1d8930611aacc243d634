/**
 * 企业微信AI工作台测试代理
 * 
 * 封装所有与企业微信AI工作台相关的测试方法
 */

import { AndroidAgent } from '@midscene/android';
import { sleep, log } from '../utils/common';
import { checkShouldExit } from '../utils/exit-handler';

export class WecomAIWorkspaceTester {
  private agent: AndroidAgent;

  constructor(agent: AndroidAgent) {
    this.agent = agent;
  }

  /**
   * 打开企业微信并进入工作台
   */
  async openWecomAndNavigateToWorkbench(): Promise<void> {
    log('打开企业微信...', 'info');
    await this.agent.launch('com.tencent.wework/.launch.LaunchSplashActivity');
    await this.agent.aiWaitFor('企业微信应用首页完全加载或出现明确的登录/主界面元素', { timeoutMs: 30000 });

    log('进入工作台...', 'info');
    await this.agent.aiTap('底部导航栏的工钮');
    await this.agent.aiWaitFor('工作台页面加载完成', { timeoutMs: 15000 });
    
    log('成功进入工作台', 'success');
  }

  /**
   * 搜索并打开AI工作台
   */
  async openAIWorkspace(): Promise<void> {
    log('搜索并打开AI工作台...', 'info');
    checkShouldExit();
    
    await this.agent.aiTap('搜索按钮或搜索框');
    await this.agent.aiInput('AI工作台', '搜索输入框');
    await this.agent.aiWaitFor('搜索结果中出现AI工作台的应用', { timeoutMs: 10000 });
    await this.agent.aiTap('搜索结果中的AI工作台应用');
    await this.agent.aiWaitFor('AI工作台应用加载完成', { timeoutMs: 20000 });
    await this.agent.aiAssert('当前在AI工作台应用的主界面');
    
    log('成功打开AI工作台', 'success');
  }

  /**
   * 测试AI工作台基本功能
   */
  async testAIWorkspaceBasicFeatures(): Promise<void> {
    log('测试AI工作台基本功能...', 'info');
    checkShouldExit();
    
    await this.agent.aiInput('你好，请介绍一下你的功能', '对话输入框');
    await this.agent.aiTap('发送按钮');
    await this.agent.aiWaitFor('AI助手完成回复', { timeoutMs: 60000 });
    await this.agent.aiAssert('页面上显示了AI助手的回复内容');
    
    log('AI工作台基本功能测试成功', 'success');
  }

  /**
   * 导航到创新社区
   */
  async navigateToInnovationCommunity(): Promise<void> {
    log('导航到创新社区...', 'info');
    checkShouldExit();
    
    await this.agent.aiTap('底部导航栏的发现按钮');
    await this.agent.aiWaitFor('发现页面加载完成', { timeoutMs: 15000 });

    await this.agent.aiTap('创新社区入口');
    await this.agent.aiWaitFor('创新社区页面加载完成', { timeoutMs: 20000 });
    
    log('成功进入创新社区', 'success');
  }

  /**
   * 打开"扣子"智能体
   */
  async openKouziAgent(): Promise<void> {
    log('打开"扣子"智能体...', 'info');
    checkShouldExit();
    
    await this.agent.aiTap('包含"扣子"文字的智能体');
    await this.agent.aiWaitFor('智能体对话界面加载完成', { timeoutMs: 20000 });
    await this.agent.aiAssert('当前在智能体的对话界面');
    
    log('成功打开"扣子"智能体', 'success');
  }

  /**
   * 执行完整的AI工作台测试流程
   */
  async runFullTest(): Promise<void> {
    log('开始执行企业微信AI工作台完整测试流程...', 'info');
    
    await this.openWecomAndNavigateToWorkbench();
    await this.openAIWorkspace();
    await this.testAIWorkspaceBasicFeatures();
    await this.navigateToInnovationCommunity();
    await this.openKouziAgent();
    
    log('企业微信AI工作台流程测试完成', 'success');
    return;
  }
}