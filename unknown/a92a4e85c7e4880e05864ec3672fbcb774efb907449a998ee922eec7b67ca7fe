/**
 * "扣子"智能体测试代理
 * 
 * 封装所有与"扣子"智能体测试相关的方法
 */

import { AndroidAgent } from '@midscene/android';
import { sleep, log } from '../utils/common';
import { checkShouldExit } from '../utils/exit-handler';

export class KouziAgentTester {
  private agent: AndroidAgent;

  constructor(agent: AndroidAgent) {
    this.agent = agent;
  }

  /**
   * 执行完整的"扣子"智能体测试流程
   */
  async runFullTest(): Promise<void> {
    log('开始"扣子"智能体测试流程...', 'info');
    
    await this.verifyBasicInterface();
    await this.testBasicDialogues();
    await this.testSuggestions();
    await this.testAdvancedInteractions();
    
    log('"扣子"智能体测试完成', 'success');
  }

  /**
   * 验证基础界面元素
   */
  async verifyBasicInterface(): Promise<void> {
    log('验证智能体界面基础元素...', 'info');
    
    // 验证智能体标题和欢迎语
    await this.agent.aiAssert('页面标题包含"扣子"');
    await sleep(1000); // 操作间短暂等待
    
    // 验证智能体初始欢迎语和建议问题
    await this.agent.aiAssert('进入智能体聊天页面');
    await sleep(1000);
    
    log('基础界面验证完成', 'success');
  }

  /**
   * 测试基本对话功能
   */
  async testBasicDialogues(): Promise<void> {
    log('测试基本对话功能...', 'info');
    checkShouldExit();
    
    // 发送通用问题并验证回复
    await this.sendMessageAndVerifyResponse(
      '简短介绍一下北京今天天气',
      '页面显示了关于天气的回复'
    );
    
    // 智能体自我介绍测试
    await this.sendMessageAndVerifyResponse(
      '你好，请介绍一下你自己',
      '页面显示了关于自身的介绍'
    );
    
    // 特殊物品信息查询功能测试
    await this.sendMessageAndVerifyResponse(
      '你对什么比较特长',
      '页面显示了回复内容，可能包含作图相关信息'
    );
    
    log('基本对话功能测试完成', 'success');
  }

  /**
   * 测试建议问题功能
   */
  async testSuggestions(): Promise<void> {
    log('测试建议问题功能...', 'info');
    checkShouldExit();
    
    await this.agent.aiWaitFor("没有loading动画，ai回复已结束", { timeoutMs: 60000 }); // 确保所有交互完成
    await this.agent.aiAssert('页面显示了建议问题');
    await this.agent.aiTap('建议问题中的任意一个');
    await this.agent.aiWaitFor('AI助手完成回复', { timeoutMs: 60000 });
    await this.agent.aiAssert('页面显示了回复内容');
    await sleep(2000);
    
    log('建议问题功能测试完成', 'success');
  }

  /**
   * 测试高级交互功能
   */
  async testAdvancedInteractions(): Promise<void> {
    log('测试高级交互功能...', 'info');
    checkShouldExit();
    
    await this.testPhotoSending();
    await this.testWebpageReference();
    await this.testClearConversation();
    await this.testInterruptionFeature();
    
    log('高级交互功能测试完成', 'success');
  }

  /**
   * 测试拍照发送功能
   */
  private async testPhotoSending(): Promise<void> {
    log('测试拍照发送功能...', 'info');
    
    await this.agent.aiAssert('页面存在一个加号形状的按钮，通常位于输入框附近用于添加附件');
    await this.agent.aiTap('加号形状的按钮');
    await this.agent.aiTap('拍照按钮');
    await sleep(1000); // 等待相机启动
    await this.agent.aiTap('拍照按钮'); // 执行拍照
    await sleep(1000); // 等待拍照完成
    await this.agent.ai('点击对勾，然后点击确定按钮使得图片发送'); // 确认发送图片
    await this.agent.aiAssert('图片发送出去的迹象或聊天记录中出现图片');
    await sleep(2000);
  }

  /**
   * 测试网页引用功能
   */
  private async testWebpageReference(): Promise<void> {
    log('测试网页引用功能...', 'info');
    
    const hasReferencedWebpage = await this.agent.aiQuery('页面是否存在引用的网页');
    if (hasReferencedWebpage) {
      await this.agent.aiTap('引用的网页方块');
      await this.agent.aiWaitFor('网页加载完成', { timeoutMs: 10000 });
      await this.agent.ai('向下滑动网页');
      await sleep(2000); // 等待滑动完成
      await this.agent.aiTap('返回按钮');
      await this.agent.aiWaitFor('返回到聊天页面', { timeoutMs: 5000 });
    } else {
      log('未发现网页引用，跳过此测试', 'info');
    }
  }

  /**
   * 测试清空对话功能
   */
  private async testClearConversation(): Promise<void> {
    log('测试清空对话功能...', 'info');
    
    await this.agent.aiTap('右上角扫把形状的清空对话按钮');
    await this.agent.aiAssert('页面不再存在之前的对话内容或显示欢迎语和建议问题状态');
    await sleep(1000);
  }

  /**
   * 测试打断功能
   */
  private async testInterruptionFeature(): Promise<void> {
    log('测试打断功能...', 'info');
    
    // 检查页面是否存在建议问题区域
    const hasSuggestionArea = await this.agent.aiQuery('页面是否存在建议问题区域或任何建议问题按钮');
    if (hasSuggestionArea) {
      await this.agent.aiAssert('页面聊天窗口内存在建议问题区域或任何建议问题按钮');
      await this.agent.ai('点击聊天框内任意一个建议问题');
      // 等待AI开始回复，然后立即打断
      await this.agent.aiWaitFor('AI开始回复', { timeoutMs: 10000 });
      await this.agent.aiTap('左下角打断按钮');
      await this.agent.aiAssert('出现新的欢迎语'); // 验证打断后是否出现新的欢迎语
    } else {
      log('未发现建议问题区域，跳过打断功能测试', 'info');
    }
    
    await this.agent.aiWaitFor("没有loading动画，ai回复已结束", { timeoutMs: 30000 }); // 确保所有交互完成
  }

  /**
   * 发送消息并验证回复的通用方法
   */
  private async sendMessageAndVerifyResponse(message: string, expectedResponse: string): Promise<void> {
    await this.agent.aiInput(message, '对话输入框');
    await this.agent.aiTap('发送按钮');
    await this.agent.aiWaitFor('AI助手完成回复', { timeoutMs: 60000 });
    await this.agent.aiAssert(expectedResponse);
    await sleep(2000);
  }

  /**
   * 返回到创新社区
   */
  async returnToCommunity(): Promise<void> {
    log('返回到创新社区...', 'info');
    
    await this.agent.aiTap('X按钮或返回按钮');
    await this.agent.aiWaitFor('已返回到创新社区页面', { timeoutMs: 15000 });
    await this.agent.aiAssert('当前在创新社区页面');
    
    log('已成功返回创新社区', 'success');
  }
}