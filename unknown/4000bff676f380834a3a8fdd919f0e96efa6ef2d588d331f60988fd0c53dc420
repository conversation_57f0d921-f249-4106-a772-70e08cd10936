/**
 * 通用工具函数
 */

/**
 * 创建一个延时函数
 * @param ms 延时的毫秒数
 * @returns Promise对象
 */
export const sleep = (ms: number): Promise<void> => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * 格式化日志信息
 * @param message 日志消息
 * @param type 日志类型
 */
export function log(message: string, type: 'info' | 'error' | 'success' = 'info'): void {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = {
    info: '🔵',
    error: '🔴',
    success: '🟢'
  }[type];
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

/**
 * 格式化测试步骤日志
 * @param step 步骤编号
 * @param message 步骤描述
 */
export function logStep(step: number, message: string): void {
  log(`步骤 ${step}: ${message}`, 'info');
}

/**
 * 截断字符串到指定长度
 * @param text 原始文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength - 3)}...`;
}