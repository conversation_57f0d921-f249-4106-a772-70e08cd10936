/**
 * 优雅退出处理工具
 * 
 * 处理SIGINT信号，实现资源清理和优雅退出
 */

import { log } from './common';

// 全局变量，用于跟踪是否需要因SIGINT退出
export let shouldExit = false;

/**
 * 设置退出处理程序
 * 注册SIGINT信号处理器
 */
export function setupExitHandlers(): void {
  process.on('SIGINT', async () => {
    log('\n正在优雅退出...', 'info');
    shouldExit = true;
  });

  // 可选：添加其他信号处理
  process.on('SIGTERM', async () => {
    log('\n收到SIGTERM信号，正在退出...', 'info');
    shouldExit = true;
  });
}

/**
 * 检查是否需要退出的函数
 * 在长时间操作前调用此函数以确保可以及时响应退出请求
 */
export function checkShouldExit(): void {
  if (shouldExit) {
    log('正在清理资源并退出...', 'info');
    process.exit(0);
  }
}

/**
 * 清理资源并退出
 * @param message 退出消息
 * @param exitCode 退出代码
 */
export function cleanupAndExit(message: string = '程序优雅退出', exitCode: number = 0): void {
  log(message, exitCode === 0 ? 'success' : 'error');
  process.exit(exitCode);
}