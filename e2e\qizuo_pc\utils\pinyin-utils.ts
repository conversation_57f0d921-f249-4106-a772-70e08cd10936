/**
 * 拼音工具函数
 * 用于将中文转换为拼音
 */

// 由于项目中没有安装拼音转换库，这里提供一个简单的中文转拼音映射
// 实际项目中建议使用 pinyin-pro 库: npm install pinyin-pro
const CHINESE_TO_PINYIN_MAP: Record<string, string> = {
  '农业': 'nongye',
  '建筑': 'jianzhu',
  '医疗健康': 'yiliaojiankang',
  '教育': 'jiaoyu',
  '金融': 'jinrong',
  '科技': 'keji',
  '互联网': 'hulianwang',
  '人工智能': 'rengongzhineng',
  '新能源': 'xinnengyuan',
  '文化': 'wenhua',
  '旅游': 'lvyou',
  '餐饮': 'canyin',
  '零售': 'lingshou',
  '物流': 'wuliu',
  '房地产': 'fangdichan',
  '制造业': 'zhizaoye',
  '服务业': 'fuwuye',
  '环保': 'huanbao',
  '能源': 'nengyuan',
  '交通': 'jiaotong',
  '通信': 'tongxin',
  '电子': 'dianzi',
  '软件': 'ruanjian',
  '硬件': 'yingjian',
  '生物': 'shengwu',
  '化工': 'huagong',
  '材料': 'cailiao',
  '机械': 'jixie',
  '汽车': 'qiche',
  '家居': 'jiaju',
  '服装': 'fuzhuang',
  '食品': 'shipin',
  '医药': 'yiyao',
  '健康': 'jiankang',
  '美容': 'meirong',
  '体育': 'tiyu',
  '娱乐': 'yule',
  '媒体': 'meiti',
  '广告': 'guanggao',
  '设计': 'sheji',
  '咨询': 'zixun',
  '法律': 'falv',
  '会计': 'kuaiji',
  '人力资源': 'renliziyuan',
  '电商': 'dianshang',
  '社交': 'shejiao',
  '游戏': 'youxi',
  '动漫': 'dongman',
  '影视': 'yingshi',
  '音乐': 'yinyue',
  '出版': 'chuban',
  '教育培训': 'jiaoyupeixun',
  '婚恋': 'hunlian',
  '母婴': 'muying',
  '宠物': 'chongwu',
  '家政': 'jiazheng',
  '养老': 'yanglao',
  '保险': 'baoxian',
  '证券': 'zhengquan',
  '投资': 'touzi',
  '众筹': 'zhongchou',
  '支付': 'zhifu',
  '区块链': 'qukuailian',
  '大数据': 'dashuju',
  '云计算': 'yunjisuan',
  '物联网': 'wulianwang',
  '智能硬件': 'zhinengyingjian',
  '机器人': 'jiqiren',
  '无人机': 'wurenjiqi',
  '虚拟现实': 'xunixianshi',
  '增强现实': 'zengqiangxianshi',
  '人脸识别': 'renlianshipei',
  '语音识别': 'yuyinshipei',
  '图像识别': 'tuxiangshipei',
  '自然语言处理': 'ziranyuyanchuli',
  '智能家居': 'zhinengjiaju',
  '智能交通': 'zhinengjiaotong',
  '智能医疗': 'zhinengyiliao',
  '智能教育': 'zhinengjiaoyu',
  '智能零售': 'zhinenglingshou',
  '智能制造': 'zhinengzhizao',
  '智能农业': 'zhinengnongye',
  '智能安防': 'zhinenganfang',
  '智能物流': 'zhinengwuliu',
  '智能客服': 'zhinengkefu',
  '智能金融': 'zhinengjinrong',
  '智能营销': 'zhinengyingxiao',
  '智能办公': 'zhinengbangong',
  '智能城市': 'zhinengchengshi',
  '智能环保': 'zhinenghuanbao',
  '智能能源': 'zhinengnengyuan',
  '智能建筑': 'zhinengjianzhu',
  '智能家电': 'zhinengjiadian',
  '智能穿戴': 'zhinengchuandai',
  '智能机器人': 'zhinengjiqiren',
  '智能无人机': 'zhinengwurenji',
  '智能安全': 'zhinenganquan',
  '智能软件': 'zhinengruanjian',
  '智能服务': 'zhinengfuwu',
  '智能产品': 'zhinengchanpin',
  '智能解决方案': 'zhinengjiejuefangan',
  '智能技术': 'zhinengjishu',
  '智能应用': 'zhinengyingyong',
  '智能平台': 'zhinengpingtai',
  '智能系统': 'zhinengxitong',
  '智能设备': 'zhinengshebei',
  '智能终端': 'zhinengzhongduan',
  '智能网络': 'zhinengwangluo',
  '智能通信': 'zhinengtongxin',
  '智能电子': 'zhinengdianzi',
  '智能芯片': 'zhinengxinpian',
  '智能传感器': 'zhinengchuanganqi',
  '智能控制': 'zhinengkongzhi',
  '智能监控': 'zhinengjiankong',
  '智能识别': 'zhinengshipei',
  '智能分析': 'zhinengfenxi',
  '智能决策': 'zhinengjuece',
  '智能预测': 'zhinengyuce',
  '智能推荐': 'zhinengtuijian',
  '智能搜索': 'zhinengsousuo',
  '智能导航': 'zhinengdaohang',
  '智能翻译': 'zhinengfanyi',
  '智能问答': 'zhinengwenda',
  '智能销售': 'zhinengxiaoshou',
  '智能管理': 'zhinengguanli',
  '智能协作': 'zhinengxiezuo',
  '智能会议': 'zhinenghuiyi',
  '智能培训': 'zhinengpeixun',
  '智能学习': 'zhinengxuexi',
  '智能健康': 'zhinengjiankang',
  '智能养老': 'zhinengyanglao',
  '智能保险': 'zhinengbaoxian',
  '智能证券': 'zhinengzhengquan',
  '智能投资': 'zhinengtouzi',
  '智能支付': 'zhinengzhifu',
  '智能理财': 'zhinenglicai',
  '智能信贷': 'zhinengxindai',
  '智能风控': 'zhinengfengkong',
  '智能征信': 'zhinengzhengxin',
  '智能合约': 'zhinengheyue',
  '智能区块链': 'zhinengqukuailian',
  '智能大数据': 'zhinengdashuju',
  '智能云计算': 'zhinengyunjisuan',
  '智能物联网': 'zhinengwulianwang',
  '智能5G': 'zhineng5G',
  '智能边缘计算': 'zhinengbianyuanjisuan',
  '智能隐私': 'zhinengyinsi',
  '智能防护': 'zhinengfanghu',
  '智能预警': 'zhinengyujing',
  '智能应急': 'zhinengyingji',
  '智能救援': 'zhinengjiuyuan',
  '智能消防': 'zhinengxiaofang',
  '智能防灾': 'zhinengfangzai',
  '智能减灾': 'zhinengjianzai',
  '智能节能': 'zhinengjieneng',
  '智能减排': 'zhinengjianpai',
  '智能资源': 'zhinengziyuan',
  '智能循环': 'zhinengxunhuan',
  '智能可持续': 'zhinengkechixu',
  '智能绿色': 'zhinenglvse',
  '智能低碳': 'zhinengditan',
  '智能碳中和': 'zhinengtanzhonghe',
  '智能碳达峰': 'zhinengtandafeng',
  '智能碳交易': 'zhinengtanjiaoyi',
  '智能碳资产': 'zhinengtanzichan',
  '智能碳金融': 'zhinengtanjinrong',
  '智能碳管理': 'zhinengtanguanli',
  '智能碳核算': 'zhinengtanhesuan',
  '智能碳监测': 'zhinengtanjiance',
  '智能碳分析': 'zhinengtanfenxi',
  '智能碳评估': 'zhinengtanpinggu',
  '智能碳咨询': 'zhinengtanzixun',
  '智能碳服务': 'zhinengtanfuwu',
  '智能碳产品': 'zhinengtanchanpin',
  '智能碳技术': 'zhinengtanjishu',
  '智能碳解决方案': 'zhinengtanjiejuefangan',
  '智能碳平台': 'zhinengtanpingtai',
  '智能碳系统': 'zhinengtanxitong',
  '智能碳应用': 'zhinengtanyingyong',
  '智能碳创新': 'zhinengtanchuangxin',
  '智能碳转型': 'zhinengtanzhuanxing',
  '智能碳发展': 'zhinengtanfazhan',
  '智能碳未来': 'zhinengtanweilai',
};

/**
 * 将中文转换为拼音
 * @param chinese 中文字符串
 * @returns 拼音字符串
 */
export function chineseToPinyin(chinese: string | string[]): string | string[] {
  // 处理数组情况
  if (Array.isArray(chinese)) {
    return chinese.map(item => {
      if (CHINESE_TO_PINYIN_MAP[item]) {
        return CHINESE_TO_PINYIN_MAP[item];
      }
      // 如果映射表中没有，则返回原字符串
      console.warn(`未找到 "${item}" 的拼音映射，请在 CHINESE_TO_PINYIN_MAP 中添加`);
      return item;
    });
  }

  // 处理单个字符串情况
  if (CHINESE_TO_PINYIN_MAP[chinese]) {
    return CHINESE_TO_PINYIN_MAP[chinese];
  }

  // 如果映射表中没有，则返回原字符串
  console.warn(`未找到 "${chinese}" 的拼音映射，请在 CHINESE_TO_PINYIN_MAP 中添加`);
  return chinese;
}