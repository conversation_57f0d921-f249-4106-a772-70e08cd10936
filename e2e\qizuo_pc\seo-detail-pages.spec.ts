// @ts-check
import { test } from "./fixture";
import dotenv from "dotenv";
import { expect } from "@playwright/test";
import { SEOValidatorPage } from "./pages/seo-validator.page";
import fs from 'fs';
import path from 'path';

// 加载环境变量
dotenv.config();

// 创建报告目录
const reportDir = path.join(process.cwd(), 'reports', 'seo-reports');
if (!fs.existsSync(reportDir)) {
  fs.mkdirSync(reportDir, { recursive: true });
}

test.describe('企座网站详情页SEO验证测试', () => {
  // 在每个测试后保存SEO报告
  test.afterEach(async ({ page }, testInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const report = await seoValidator.generateSEOReport();

    const reportPath = path.join(reportDir, `${testInfo.title.replace(/\s+/g, '-')}.txt`);
    fs.writeFileSync(reportPath, report);
    console.log(`SEO报告已保存至: ${reportPath}`);
  });

  // 项目详情页测试
  test('项目详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 先访问项目列表页
    await page.goto(`${process.env.QIZUO_PC_URL!}/xiangmu`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 点击第一个项目
    await page.locator('.project-card, .project-item').first().click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前项目名称
    const title = await page.title();
    const projectName = title.split('-')[0].trim();

    // 获取SEO元素
    const seoElements = await seoValidator.getSEOElements();

    // 创建错误收集数组
    const errors: string[] = [];

    // 使用软断言验证标题包含项目名称
    if (!seoElements.title.includes(projectName)) {
      errors.push(`标题验证失败: 标题 "${seoElements.title}" 应包含项目名称 "${projectName}"`);
    }

    // 验证关键词包含"详细介绍"
    if (seoElements.keywords) {
      if (!seoElements.keywords.toLowerCase().includes('详细介绍')) {
        errors.push(`关键词验证失败: 关键词 "${seoElements.keywords}" 应包含 "详细介绍"`);
      }
    } else {
      errors.push('关键词验证失败: meta keywords元素不存在');
    }

    // 验证描述包含"企座"
    if (seoElements.description) {
      if (!seoElements.description.toLowerCase().includes('企座')) {
        errors.push(`描述验证失败: 描述 "${seoElements.description}" 应包含 "企座"`);
      }
    } else {
      errors.push('描述验证失败: meta description元素不存在');
    }

    // 验证URL格式
    const urlRegex = /xiangmu\.aitojoy\.com\/\d+/;
    if (!urlRegex.test(page.url())) {
      errors.push(`URL验证失败: URL "${page.url()}" 应匹配格式 "xiangmu.aitojoy.com/数字ID"`);
    }

    // 输出验证结果
    if (errors.length > 0) {
      console.log('\n===== 项目详情页SEO验证失败 =====');
      errors.forEach(error => console.log(`❌ ${error}`));
      console.log('================================\n');
    } else {
      console.log('\n✅ 项目详情页SEO验证全部通过\n');
    }

    // 最后断言是否全部通过
    expect(errors.length, `SEO验证失败:\n${errors.join('\n')}`).toBe(0);
  });

  // 活动详情页测试
  test('活动详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 先访问活动列表页
    await page.goto(`${process.env.QIZUO_PC_URL!}/huodong`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 点击第一个活动
    await page.locator('.activity-card, .activity-item').first().click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取SEO元素
    const seoElements = await seoValidator.getSEOElements();

    // 创建错误收集数组
    const errors: string[] = [];

    // 验证标题格式
    if (!seoElements.title.toLowerCase().includes('企座')) {
      errors.push(`标题验证失败: 标题 "${seoElements.title}" 应包含 "企座"`);
    }

    // 验证关键词
    if (seoElements.keywords) {
      const hasExpectedKeywords =
        seoElements.keywords.toLowerCase().includes('嘉宾名单') ||
        seoElements.keywords.toLowerCase().includes('详细议程');

      if (!hasExpectedKeywords) {
        errors.push(`关键词验证失败: 关键词 "${seoElements.keywords}" 应包含 "嘉宾名单" 或 "详细议程"`);
      }
    } else {
      errors.push('关键词验证失败: meta keywords元素不存在');
    }

    // 验证描述
    if (seoElements.description) {
      if (!seoElements.description.toLowerCase().includes('提供')) {
        errors.push(`描述验证失败: 描述 "${seoElements.description}" 应包含 "提供"`);
      }
    } else {
      errors.push('描述验证失败: meta description元素不存在');
    }

    // 验证URL格式
    const urlRegex = /huodong\.aitojoy\.com\/\d+/;
    if (!urlRegex.test(page.url())) {
      errors.push(`URL验证失败: URL "${page.url()}" 应匹配格式 "huodong.aitojoy.com/数字ID"`);
    }

    // 输出验证结果
    if (errors.length > 0) {
      console.log('\n===== 活动详情页SEO验证失败 =====');
      errors.forEach(error => console.log(`❌ ${error}`));
      console.log('================================\n');
    } else {
      console.log('\n✅ 活动详情页SEO验证全部通过\n');
    }

    // 最后断言是否全部通过
    expect(errors.length, `SEO验证失败:\n${errors.join('\n')}`).toBe(0);
  });

  // 课程详情页测试
  test('课程详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 先访问课程列表页
    await page.goto(`${process.env.QIZUO_PC_URL!}/ketang`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 点击第一个课程
    await page.locator('.course-card, .course-item').first().click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取SEO元素
    const seoElements = await seoValidator.getSEOElements();

    // 创建错误收集数组
    const errors: string[] = [];

    // 验证标题格式
    if (!seoElements.title.toLowerCase().includes('企座')) {
      errors.push(`标题验证失败: 标题 "${seoElements.title}" 应包含 "企座"`);
    }

    // 验证关键词
    if (seoElements.keywords) {
      if (!seoElements.keywords.toLowerCase().includes('精选课程')) {
        errors.push(`关键词验证失败: 关键词 "${seoElements.keywords}" 应包含 "精选课程"`);
      }
    } else {
      errors.push('关键词验证失败: meta keywords元素不存在');
    }

    // 验证URL格式
    const urlRegex = /ketang\.aitojoy\.com\/\d+/;
    if (!urlRegex.test(page.url())) {
      errors.push(`URL验证失败: URL "${page.url()}" 应匹配格式 "ketang.aitojoy.com/数字ID"`);
    }

    // 输出验证结果
    if (errors.length > 0) {
      console.log('\n===== 课程详情页SEO验证失败 =====');
      errors.forEach(error => console.log(`❌ ${error}`));
      console.log('================================\n');
    } else {
      console.log('\n✅ 课程详情页SEO验证全部通过\n');
    }

    // 最后断言是否全部通过
    expect(errors.length, `SEO验证失败:\n${errors.join('\n')}`).toBe(0);
  });

  // 政策详情页测试
  test('政策详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 先访问政策列表页
    await page.goto(`${process.env.QIZUO_PC_URL!}/zhengce`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 点击第一个政策
    await page.locator('.policy-card, .policy-item').first().click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取SEO元素
    const seoElements = await seoValidator.getSEOElements();

    // 创建错误收集数组
    const errors: string[] = [];

    // 验证标题格式
    const hasPolicyTitle =
      (seoElements.title && seoElements.title.toLowerCase().includes('政策')) ||
      (seoElements.title && seoElements.title.toLowerCase().includes('ai解读'));

    if (!hasPolicyTitle) {
      errors.push(`标题验证失败: 标题 "${seoElements.title}" 应包含 "政策" 或 "AI解读"`);
    }

    // 验证关键词
    if (seoElements.keywords) {
      const hasExpectedKeywords =
        seoElements.keywords.toLowerCase().includes('ai解读') ||
        seoElements.keywords.toLowerCase().includes('要点介绍');

      if (!hasExpectedKeywords) {
        errors.push(`关键词验证失败: 关键词 "${seoElements.keywords}" 应包含 "AI解读" 或 "要点介绍"`);
      }
    } else {
      errors.push('关键词验证失败: meta keywords元素不存在');
    }

    // 验证描述
    if (seoElements.description) {
      if (!seoElements.description.toLowerCase().includes('为您详细解读')) {
        errors.push(`描述验证失败: 描述 "${seoElements.description}" 应包含 "为您详细解读"`);
      }
    } else {
      errors.push('描述验证失败: meta description元素不存在');
    }

    // 验证URL格式
    const urlRegex = /zhengce\.aitojoy\.com\/\d+/;
    if (!urlRegex.test(page.url())) {
      errors.push(`URL验证失败: URL "${page.url()}" 应匹配格式 "zhengce.aitojoy.com/数字ID"`);
    }

    // 输出验证结果
    if (errors.length > 0) {
      console.log('\n===== 政策详情页SEO验证失败 =====');
      errors.forEach(error => console.log(`❌ ${error}`));
      console.log('================================\n');
    } else {
      console.log('\n✅ 政策详情页SEO验证全部通过\n');
    }

    // 最后断言是否全部通过
    expect(errors.length, `SEO验证失败:\n${errors.join('\n')}`).toBe(0);
  });
});
