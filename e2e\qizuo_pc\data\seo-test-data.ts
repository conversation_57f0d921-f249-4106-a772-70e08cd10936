/**
 * SEO测试数据
 * 基于data/tkd.csv文件中的数据
 */
import { chineseToPinyin } from '../utils/pinyin-utils';

export class SEOTestData {
  // 首页
  static homePage = {
    title: "一站式独角兽企业孵化服务平台 - 企座",
    keywords: [
      "独角兽孵化",
      "企业家服务",
      "科技金融服务",
      "产业服务"
    ],
    description: "企座平台在集团30年企业服务沉淀基础上，依托大数据与人工智能技术，为企业提供独角兽孵化、科技金融、产业合作并购、企业家健康商旅等服务。即刻探索企座平台，一站式满足您的企业服务需求。",
    url: "www.aitojoy.com"
  };

  // 全局搜索页
  static searchPage = {
    // title会根据搜索关键词动态生成，在测试中单独处理
    keywords: [
      "创新项目搜索",
      "商机活动搜索",
      "企业搜索",
      "机构搜索",
      "人脉搜索",
      "服务搜索",
      "课程搜索",
      "政策搜索"
    ],
    description: "企座搜索提供企业服务全局搜索，覆盖创新项目、商机活动、企业、机构、人脉、服务、课程、政策等模块，精准匹配您的需求。立即查找所需服务，加速商业成长",
    // url会根据搜索关键词动态生成，在测试中单独处理
    url: "so.aitojoy.com"
  };

  // 找项目-列表页
  static projectListPage = {
    title: "找项目 找合作 找合伙-企座",
    keywords: [
      "创业项目孵化",
      "创新项目合作联营",
      "项目详细介绍",
      "项目企业信息",
      "项目AI解读",
      "项目产业链信息"
    ],
    description: "企座平台提供50万+创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。支持有合作意向客户在线对接项目合作联营。",
    url: "xiangmu.aitojoy.com"
  };

  // 找项目-列表页-行业筛选
  static projectListPageIndustryFilter = (industry: string | string[]) => {
    // 处理单个行业或多个行业
    const industryStr = Array.isArray(industry) ? industry.join('') : industry;
    
    // 将中文行业名称转换为拼音
    const industryPinyin = chineseToPinyin(industry);
    
    // 处理URL部分，如果是数组则用逗号连接
    const industryUrlPart = Array.isArray(industryPinyin) ? industryPinyin.join(',') : industryPinyin;

    return {
      title: `${industryStr}的优质项目 - 企座`,
      keywords: [
        `${industryStr}创业项目孵化`,
        `${industryStr}创新项目合作联营`,
        `${industryStr}项目详细介绍`,
        `${industryStr}项目企业信息`,
        `${industryStr}项目AI解读`,
        `${industryStr}项目产业链信息`
      ],
      description: `企座平台提供${industryStr}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。支持有合作意向客户在线对接项目合作联营。`,
      url: `xiangmu.aitojoy.com/hangye-${industryUrlPart}`
    };
  };

  // 找项目-列表页-行业筛选 (用于测试实际URL)
  static projectListPageIndustryFilterForTest = (industry: string | string[]) => {
    // 处理单个行业或多个行业
    const industryStr = Array.isArray(industry) ? industry.join('') : industry;
    
    // 将中文行业名称转换为拼音
    const industryPinyin = chineseToPinyin(industry);
    
    // 处理URL部分，如果是数组则用逗号连接
    const industryUrlPart = Array.isArray(industryPinyin) ? industryPinyin.join(',') : industryPinyin;

    return {
      title: `${industryStr}的优质项目 - 企座`,
      keywords: [
        `${industryStr}创业项目孵化`,
        `${industryStr}创新项目合作联营`,
        `${industryStr}项目详细介绍`,
        `${industryStr}项目企业信息`,
        `${industryStr}项目AI解读`,
        `${industryStr}项目产业链信息`
      ],
      description: `企座平台提供${industryStr}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。支持有合作意向客户在线对接项目合作联营。`,
      url: `hyfl-${industryUrlPart}`
    };
  }

  // 找项目-列表页-国家地区筛选
  static projectListPageRegionFilter = (region: string | string[]) => {
    // 处理单个地区或省市二级地区
    const isArray = Array.isArray(region);
    const regionStr = isArray ? region.join('') : region;

    // 生成URL部分
    let urlPart = "";
    if (isArray) {
      // 省市二级地区，例如：["浙江省", "杭州市"] => "zhejiangsheng-hangzhoushi"
      // 这里假设传入的是拼音形式，如果是中文需要转换
      urlPart = region.join('-');
    } else {
      // 单个地区
      urlPart = region;
    }

    return {
      title: `${regionStr}的优质项目`,
      keywords: [
        `${regionStr}创业项目孵化`,
        `${regionStr}创新项目合作联营`,
        `${regionStr}项目详细介绍`,
        `${regionStr}项目企业信息`,
        `${regionStr}项目AI解读`,
        `${regionStr}项目产业链信息`
      ],
      description: `企座平台提供${regionStr}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。同时支持有合作意向客户在线对接项目合作联营。`,
      url: `xiangmu.aitojoy.com/diqu-${urlPart}`
    };
  };

  // 找项目-列表页-合作金额筛选
  static projectListPageAmountFilter = (amountRange: string) => ({
    title: `合作金额${amountRange}的优质项目`,
    keywords: [
      `${amountRange}创业项目孵化`,
      `${amountRange}创新项目合作联营`,
      `${amountRange}项目详细介绍`,
      `${amountRange}项目企业信息`,
      `${amountRange}项目AI解读`,
      `${amountRange}项目产业链信息`
    ],
    description: `企座平台提供${amountRange}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。同时支持有合作意向客户在线对接项目合作联营。`,
    url: "xiangmu.aitojoy.com/jine-"
  });

  /**
   * 组合筛选的SEO数据生成
   * @param region 地区，可以是省份或城市
   * @param industry 行业，可以是单个行业或行业数组
   * @param amountRange 金额范围
   * @param generateUrl 是否生成完整URL，默认为true
   * @returns SEO数据对象
   */
  static projectListPageCombinedFilter = (
    region: string | string[] = '',
    industry: string | string[] = '',
    amountRange: string = '',
    generateUrl: boolean = true
  ) => {
    // 处理地区
    const regionStr = Array.isArray(region) ? region.join('') : region;

    // 处理行业
    const industryStr = Array.isArray(industry) ? industry.join('') : industry;

    // 生成标题，如果所有参数都为空，则使用"优质项目"
    const title = `${regionStr}${industryStr}${amountRange}的优质项目`.replace(/^的优质项目$/, '优质项目');

    // 生成关键词
    const keywords = [
      `${regionStr}${industryStr}${amountRange}创业项目孵化`,
      `${regionStr}${industryStr}${amountRange}创新项目合作联营`,
      `${regionStr}${industryStr}${amountRange}项目详细介绍`,
      `${regionStr}${industryStr}${amountRange}项目企业信息`,
      `${regionStr}${industryStr}${amountRange}项目AI解读`,
      `${regionStr}${industryStr}${amountRange}项目产业链信息`
    ];

    // 生成描述
    const description = `企座平台提供${regionStr}${industryStr}${amountRange}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。同时支持有合作意向客户在线对接项目合作联营。`;

    // 生成URL
    let url = "xiangmu.aitojoy.com";

    if (generateUrl) {
      // 构建URL部分
      const urlParts: string[] = [];

      // 添加行业部分
      if (industry && industry.length > 0) {
        const industryPart = Array.isArray(industry)
          ? industry.map(ind => ind).join(',')
          : industry;
        urlParts.push(`hangye-${industryPart}`);
      }

      // 添加地区部分
      if (region && region.length > 0) {
        const regionPart = Array.isArray(region)
          ? region.join('-')
          : region;
        urlParts.push(`diqu-${regionPart}`);
      }

      // 添加金额部分
      if (amountRange) {
        // 假设金额格式为"30万-50万"，需要转换为"30wanzhi50wan"
        const formattedAmount = amountRange.replace('-', 'zhi');
        urlParts.push(`jine-${formattedAmount}`);
      }

      // 组合URL
      if (urlParts.length > 0) {
        url = `xiangmu.aitojoy.com/${urlParts.join('/')}`;
      }
    }

    return {
      title,
      keywords,
      description,
      url
    };
  };

  // 企业诊断
  static enterpriseDiagnosisPage = {
    title: "企业健康诊断系统：一键生成营销/税务风险评估报告 - 企座",
    keywords: [
      "企业健康度评分",
      "工商异常检测工具",
      "税务合规诊断系统",
      "商标侵权风险评估",
      "法律纠纷预警平台"
    ],
    description: "企座平台企业健康度评分系统，覆盖营销合规、商标风险、工商异常、法律纠纷、税务稽查等多项核心指标、3分钟获取行业对比数据及整改建议。点击免费诊断→",
    url: "qiyezhenduan.aitojoy.com"
  };

  // 聚活动-频道页
  static activityChannelPage = {
    title: "项目合作考察会 创业项目路演 行业私董会 企业家高端康养会 - 企座",
    keywords: [
      "项目合作考察",
      "创业项目路演",
      "行业私董会",
      "企业家高端康养会",
      "在线活动报名",
      "在线查看活动议程"
    ],
    description: "全国各省市举办年均超过1000场项目合作考察会、创业项目路演、行业私董会、企业家高端康养会，支持在线筛选会议、了解议程、一键报名。快速锁定优质活动，拓展人脉与商机！",
    url: "huodong.aitojoy.com"
  };

  // 聚活动-列表页
  static activityListPage = {
    title: "精选企业家活动、创业路演活动推荐 - 企座",
    keywords: [
      "最新活动推荐",
      "热门活动推荐",
      "挑选适合活动",
      "线下会议日程",
      "掌握最新商机"
    ],
    description: "企座平台为企业家、创业项目每月在全国各地举办超过100场高规格线下商机合作活动。支持有意向的客户在线了解会议内容与议程、线上报名锁定席位。",
    url: "huodong.aitojoy.com/list"
  };

  /**
   * 聚活动-列表页-举办地点筛选
   * @param location 举办地点名称
   * @param locationPinyin 举办地点全拼
   * @returns SEO数据对象
   */
  static activityListPageLocationFilter = (location: string, locationPinyin: string) => {
    return {
      title: `精选${location}企业家活动、创业路演活动推荐 - 企座`,
      keywords: [
        `${location}最新活动推荐`,
        `${location}热门活动推荐`,
        `挑选${location}适合活动`,
        `${location}线下会议日程`
      ],
      description: `企座平台为企业家、创业项目在${location}举办高规格线下商机合作活动。支持有意向的客户在线了解会议内容与议程、线上报名锁定席位。`,
      url: `huodong.aitojoy.com/list/${locationPinyin}`
    };
  };

  /**
   * 聚活动-详情页
   * @param activityName 活动名称
   * @param activityId 活动ID
   * @param province 活动所在省份
   * @param activityType 活动类型
   * @returns SEO数据对象
   */
  static activityDetailPage = (
    activityName: string,
    activityId: string,
    province: string = '',
    activityType: string = ''
  ) => {
    return {
      title: `${province}${activityType}${activityName} - 企座`,
      keywords: [
        `${activityName}嘉宾名单查询`,
        `${activityName}详细议程`,
        `立即在线预约${activityName}`
      ],
      description: `提供${province}${activityType}${activityName}详细议程、嘉宾名单，立即在线预约，获取最新行业洞察与合作机会！`,
      url: `huodong.aitojoy.com/${activityId}`
    };
  };

  // 产业服务-频道页
  static industryServiceChannelPage = {
    title: "匹配并购标的 探索产业全貌 研究地域产业集群 - 企座",
    keywords: [
      "上市公司并购推荐",
      "中小企业并购匹配",
      "产业链大数据",
      "并购标的筛选",
      "并购撮合服务"
    ],
    description: "企座平台依托产业链与企业大数据及自研大模型算法，为上市公司精准推荐并购标的，助力中小创新企业智能匹配并购方。覆盖人工智能、新能源等150+行业，提供标的筛选、估值分析、交易撮合全流程服务。",
    url: "chanyefuwu.aitojoy.com"
  };

  // 产业服务-列表页
  static industryServiceListPage = {
    title: "并购标的查询 有并购需求上市公司查询 企业产业信息查询 - 企座",
    keywords: [
      "并购需求上市公司",
      "中小企业被并购",
      "企业涉及产业信息"
    ],
    description: "聚合并购双方企业信息，基于企业与产业链大数据，提供对应企业全方位信息。有并购需求的双方企业提交资料即可获得凭条提供的目标企业筛选、匹配、对接服务。",
    url: "chanyefuwu.aitojoy.com/list"
  };

  /**
   * 产业服务-列表页-所在地筛选
   * @param region 地区名称
   * @param otherFilters 其它筛选条件全拼（可选）
   * @returns SEO数据对象
   */
  static industryServiceListPageRegionFilter = (region: string, otherFilters: string = '') => {
    const urlPart = otherFilters ? `${region}-${otherFilters}` : region;

    return {
      title: `${region}并购标的查询 有并购需求上市公司查询 企业产业信息查询 - 企座`,
      keywords: [
        `${region}并购需求上市公司`,
        `${region}中小企业被并购`,
        `${region}企业涉及产业信息`
      ],
      description: `聚合${region}并购双方企业信息，基于企业与产业链大数据，提供对应企业全方位信息。有并购需求的双方企业提交资料即可获得凭条提供的目标企业筛选、匹配、对接服务。`,
      url: `chanyefuwu.aitojoy.com/list/${urlPart}`
    };
  };

  /**
   * 产业服务-详情页
   * @param companyName 企业名称
   * @param companyId 企业ID
   * @returns SEO数据对象
   */
  static industryServiceDetailPage = (companyName: string, companyId: string) => {
    return {
      title: `${companyName}基本信息 涉足产业链 经营信息 企业舆情 知识产权 法律风险 - 企座`,
      keywords: [
        "企业基本信息",
        "企业涉足产业链",
        "企业经营信息",
        "企业舆情",
        "企业知识产权",
        "企业法律风险"
      ],
      description: `${companyName}基本信息、涉足产业链、经营信息、企业舆情、知识产权、法律风险，支持有并购合作需求企业进行对接。`,
      url: `chanyefuwu.aitojoy.com/${companyId}/`
    };
  };

  // 联营服务
  static jointOperationServicePage = {
    title: "创业项目孵化 项目加盟联营 平台精细服务赋能 - 企座",
    keywords: [
      "创业项目孵化流程",
      "联营项目合作",
      "平台精细服务",
      "产业资源对接"
    ],
    description: "企座联营服务为创业项目方提供项目发展所需客户、资金、服务等各类资源。并为联营合作项目的企业家提供联营阶段全方位支持赋能。了解平台合作流程、服务案例，立即申请成为合作方。",
    url: "lianyingfuwu.aitojoy.com"
  };

  // 创业课堂-频道页
  static courseChannelPage = {
    title: "全岗位培训课程 企业家创业干货分享 - 企座",
    keywords: [
      "企业员工学习提高平台",
      "管理岗成长课程",
      "企业一线员工提高课程",
      "企业培训SaaS系统"
    ],
    description: "企座创业课堂覆盖战略管理、专业技能、职业素养的体系化培训课程，支持在线学习，配套企业培训管理SaaS系统，可实现课程定制、学习追踪、测验考试等丰富功能。",
    url: "ketang.aitojoy.com"
  };

  // 创业课堂-列表页
  static courseListPage = {
    title: "企业培训专题课程 - 企座",
    keywords: [
      "领导力课程",
      "高管培训课程",
      "员工技能培训",
      "销售技巧学习",
      "财务管理课程",
      "人力资源课程"
    ],
    description: "企座创业课堂提供超过20个不同类型课程，适用于企业20余种不同岗位，支持在线学习。提升员工能力与企业竞争力。",
    url: "ketang.aitojoy.com/list"
  };

  /**
   * 创业课堂-列表页-课程分类筛选
   * @param category 课程分类
   * @param categoryPinyin 课程分类筛选项全拼
   * @returns SEO数据对象
   */
  static courseListPageCategoryFilter = (category: string, categoryPinyin: string) => {
    return {
      title: `${category}精选课程 - 企座`,
      keywords: [
        `${category}精选课程`,
        "员工技能培训",
        "职场能力提升"
      ],
      description: `企座为您提供${category}精选课程,支持在线学习。提升员工能力与企业竞争力。`,
      url: `ketang.aitojoy.com/list/${categoryPinyin}`
    };
  };

  /**
   * 创业课堂-列表页-课程标签筛选
   * @param tag 课程标签
   * @param tagPinyin 课程标签筛选项全拼
   * @returns SEO数据对象
   */
  static courseListPageTagFilter = (tag: string, tagPinyin: string) => {
    return {
      title: `${tag}精选课程 - 企座`,
      keywords: [
        `${tag}精选课程`,
        "员工技能培训",
        "职场能力提升"
      ],
      description: `企座为您提供${tag}精选课程,支持在线学习。提升员工能力与企业竞争力。`,
      url: `ketang.aitojoy.com/list/${tagPinyin}`
    };
  };

  /**
   * 创业课堂-列表页-适合岗位筛选
   * @param position 适合岗位
   * @param positionPinyin 适合岗位筛选项全拼
   * @returns SEO数据对象
   */
  static courseListPagePositionFilter = (position: string, positionPinyin: string) => {
    return {
      title: `${position}精选课程 - 企座`,
      keywords: [
        `${position}精选课程`,
        "员工技能培训",
        "职场能力提升"
      ],
      description: `企座为您提供${position}精选课程,支持在线学习。提升员工能力与企业竞争力。`,
      url: `ketang.aitojoy.com/list/${positionPinyin}`
    };
  };

  /**
   * 创业课堂-列表页-组合筛选
   * @param category 课程分类
   * @param tag 课程标签
   * @param position 适合岗位
   * @param categoryPinyin 课程分类筛选项全拼
   * @param tagPinyin 课程标签筛选项全拼
   * @param positionPinyin 适合岗位筛选项全拼
   * @returns SEO数据对象
   */
  static courseListPageCombinedFilter = (
    category: string = '',
    tag: string = '',
    position: string = '',
    categoryPinyin: string = '',
    tagPinyin: string = '',
    positionPinyin: string = ''
  ) => {
    const title = `${category}${tag}${position}精选课程 - 企座`;
    const keywords = [
      `${category}${tag}${position}精选课程`,
      "员工技能培训",
      "职场能力提升"
    ];
    const description = `企座为您提供${category}${tag}${position}精选课程,支持在线学习。提升员工能力与企业竞争力。`;

    // 构建URL
    const urlParts: string[] = [];
    if (categoryPinyin) urlParts.push(categoryPinyin);
    if (tagPinyin) urlParts.push(tagPinyin);
    if (positionPinyin) urlParts.push(positionPinyin);

    const url = `ketang.aitojoy.com/list/${urlParts.join('-')}`;

    return {
      title,
      keywords,
      description,
      url
    };
  };

  /**
   * 创业课堂-详情页
   * @param courseName 课程名称
   * @param courseId 课程ID
   * @param position 适合岗位
   * @param category 课程分类
   * @param tag 课程标签
   * @param courseIntro 课程简介
   * @returns SEO数据对象
   */
  static courseDetailPage = (
    courseName: string,
    courseId: string,
    position: string = '',
    category: string = '',
    tag: string = '',
    courseIntro: string = ''
  ) => {
    return {
      title: `${position}精选课程${courseName} - 企座`,
      keywords: [
        `${position}精选课程${courseName}`,
        `${category}精选课程${courseName}`,
        `${tag}精选课程${courseName}`
      ],
      description: courseIntro || `${courseName}课程简介`,
      url: `ketang.aitojoy.com/${courseId}`
    };
  };

  // 创业课堂-成长班列表页
  static courseGrowthClassListPage = {
    title: "岗位胜任成长班 从管理到一线各类岗位课程 支持在线学习 - 企座",
    keywords: [
      "岗位胜任成长班",
      "管理岗位技能学习",
      "各岗位专业能力提升"
    ],
    description: "企座岗位胜任成长班，为企业不同岗位人才提供体系化学习课程，助您轻松胜任。选择课程即可开始在线学习。",
    url: "ketang.aitojoy.com/chengzhangban"
  };

  /**
   * 创业课堂-成长班详情页
   * @param growthClassName 成长班名称
   * @param growthClassId 成长班ID
   * @returns SEO数据对象
   */
  static courseGrowthClassDetailPage = (
    growthClassName: string,
    growthClassId: string
  ) => {
    return {
      title: `${growthClassName}全部课程 - 企座`,
      keywords: [
        `${growthClassName}全部课程`
      ],
      description: `${growthClassName}，为您的岗位定制体系化学习课程，助您轻松胜任。`,
      url: `ketang.aitojoy.com/chengzhangban${growthClassId}`
    };
  };

  // 惠企政策-频道页
  static policyChannelPage = {
    title: `${new Date().getFullYear()}热点政策大全 AI政策解读 政府奖励政策代理申报 - 企座`,
    keywords: [
      `${new Date().getFullYear()}热点政策大全`,
      "AI政策解读",
      "奖励政策代理申报"
    ],
    description: "实时更新全国各省市中小企业补贴、高新企业奖励、产业扶持资金等惠企政策，提供政策匹配、智能解读、代理申报全流程服务，累积帮助企业申领补贴超10亿元。",
    url: "zhengce.aitojoy.com"
  };

  // 惠企政策-列表页-奖补政策tab
  static policyRewardListPage = {
    title: "各省奖励补贴政策查询 AI解读 代理申报 - 企座",
    keywords: [
      "奖励补贴政策查询",
      "奖励补贴政策AI解读",
      "奖励补贴政策代理申报"
    ],
    description: "实时汇总全国最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。",
    url: "zhengce.aitojoy.com/jiangbu"
  };

  /**
   * 惠企政策-列表页-奖补政策tab-发布地区筛选
   * @param region 发布地区名称
   * @param regionPinyin 发布地区全拼
   * @returns SEO数据对象
   */
  static policyRewardListPageRegionFilter = (region: string, regionPinyin: string) => {
    return {
      title: `${region}奖励补贴政策查询 AI解读 代理申报 - 企座`,
      keywords: [
        `${region}奖励补贴政策查询`,
        `${region}奖励补贴政策AI解读`,
        `${region}奖励补贴政策代理申报`
      ],
      description: `实时汇总${region}最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。`,
      url: `zhengce.aitojoy.com/jiangbu/${regionPinyin}`
    };
  };

  /**
   * 惠企政策-列表页-奖补政策tab-政策类型筛选
   * @param policyType 政策类型
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyRewardListPageTypeFilter = (policyType: string, policyTypePinyin: string) => {
    return {
      title: `${policyType}奖励补贴政策查询 AI解读 代理申报 - 企座`,
      keywords: [
        `${policyType}奖励补贴政策查询`,
        `${policyType}奖励补贴政策AI解读`,
        `${policyType}奖励补贴政策代理申报`
      ],
      description: `实时汇总${policyType}最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。`,
      url: `zhengce.aitojoy.com/jiangbu/${policyTypePinyin}`
    };
  };

  /**
   * 惠企政策-列表页-奖补政策tab-组合筛选
   * @param region 发布地区名称
   * @param policyType 政策类型
   * @param regionPinyin 发布地区全拼
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyRewardListPageCombinedFilter = (
    region: string = '',
    policyType: string = '',
    regionPinyin: string = '',
    policyTypePinyin: string = ''
  ) => {
    return {
      title: `${region}${policyType}奖励补贴政策查询 AI解读 代理申报 - 企座`,
      keywords: [
        `${region}${policyType}奖励补贴政策查询`,
        `${region}${policyType}奖励补贴政策AI解读`,
        `${region}${policyType}奖励补贴政策代理申报`
      ],
      description: `实时汇总${region}${policyType}最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。`,
      url: `zhengce.aitojoy.com/jiangbu/${regionPinyin}-${policyTypePinyin}`
    };
  };

  // 惠企政策-列表页-热点政策tab
  static policyHotListPage = {
    title: "各省热点政策查询 AI解读 - 企座",
    keywords: [
      "热点政策查询",
      "热点政策AI解读"
    ],
    description: "提供全国最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策。",
    url: "zhengce.aitojoy.com/redian"
  };

  /**
   * 惠企政策-列表页-热点政策tab-发布地区筛选
   * @param region 发布地区名称
   * @param regionPinyin 发布地区全拼
   * @returns SEO数据对象
   */
  static policyHotListPageRegionFilter = (region: string, regionPinyin: string) => {
    return {
      title: `${region}热点政策查询 AI解读 - 企座`,
      keywords: [
        `${region}热点政策查询`,
        `${region}热点政策AI解读`
      ],
      description: `提供${region}最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策。`,
      url: `zhengce.aitojoy.com/redian/${regionPinyin}`
    };
  };

  /**
   * 惠企政策-列表页-热点政策tab-适用行业筛选
   * @param industry 适用行业
   * @param industryPinyin 适用行业全拼
   * @returns SEO数据对象
   */
  static policyHotListPageIndustryFilter = (industry: string, industryPinyin: string) => {
    return {
      title: `${industry}热点政策查询 AI解读 - 企座`,
      keywords: [
        `${industry}热点政策查询`,
        `${industry}热点政策AI解读`
      ],
      description: `提供${industry}最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策。`,
      url: `zhengce.aitojoy.com/redian/${industryPinyin}`
    };
  };

  /**
   * 惠企政策-列表页-热点政策tab-政策类型筛选
   * @param policyType 政策类型
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyHotListPageTypeFilter = (policyType: string, policyTypePinyin: string) => {
    return {
      title: `${policyType}热点政策查询 AI解读 - 企座`,
      keywords: [
        `${policyType}热点政策查询`,
        `${policyType}热点政策AI解读`
      ],
      description: `提供${policyType}最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策`,
      url: `zhengce.aitojoy.com/redian/${policyTypePinyin}`
    };
  };

  /**
   * 惠企政策-列表页-热点政策tab-组合筛选
   * @param region 发布地区名称
   * @param industry 适用行业
   * @param policyType 政策类型
   * @param regionPinyin 发布地区全拼
   * @param industryPinyin 适用行业全拼
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyHotListPageCombinedFilter = (
    region: string = '',
    industry: string = '',
    policyType: string = '',
    regionPinyin: string = '',
    industryPinyin: string = '',
    policyTypePinyin: string = ''
  ) => {
    return {
      title: `${region}${industry}${policyType}热点政策查询 AI解读 - 企座`,
      keywords: [
        `${region}${industry}${policyType}热点政策查询`,
        `${region}${industry}${policyType}热点政策AI解读`
      ],
      description: `提供${region}${industry}${policyType}最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策`,
      url: `zhengce.aitojoy.com/redian/${regionPinyin}-${industryPinyin}-${policyTypePinyin}`
    };
  };

  /**
   * 惠企政策-详情页-奖补政策
   * @param policyName 政策名称
   * @param policyId 政策ID
   * @returns SEO数据对象
   */
  static policyRewardDetailPage = (policyName: string, policyId: string) => {
    return {
      title: `奖补政策${policyName}AI解读、要点介绍、申报流程 - 企座`,
      keywords: [
        `${policyName}AI解读`,
        `${policyName}要点介绍`,
        `${policyName}申报流程`
      ],
      description: `为您详细解读${policyName}背景、价值及其它相关要点，支持政策原文查阅。可在平台直接申请代理申报服务。`,
      url: `zhengce.aitojoy.com/${policyId}`
    };
  };

  /**
   * 惠企政策-详情页-热点政策
   * @param policyName 政策名称
   * @param policyId 政策ID
   * @returns SEO数据对象
   */
  static policyHotDetailPage = (policyName: string, policyId: string) => {
    return {
      title: `热点政策${policyName}AI解读、政策原文 - 企座`,
      keywords: [
        `${policyName}AI解读`,
        `${policyName}原文内容`
      ],
      description: `为您详细解读${policyName}背景、价值及其它相关要点，支持政策原文查阅。`,
      url: `zhengce.aitojoy.com/${policyId}`
    };
  };

  // 人脉广场-频道页
  static connectionChannelPage = {
    title: "创业人脉智能推荐 精准对接合作伙伴、投资人等各类企业家人脉 -企座",
    keywords: [
      "合作伙伴推荐",
      "管理合伙人推荐",
      "投资人推荐",
      "知名企业家",
      "AI人脉推荐"
    ],
    description: "企座人脉广场通过大数据+AI大模型算法，为创业者与企业家智能推荐合作伙伴、管理合伙人、投资人各类优质人脉。注册完善基本信息后，即可使用智能推荐服务！",
    url: "renmai.aitojoy.com"
  };

  // 人脉广场-列表页
  static connectionListPage = {
    title: "企业家 投资人 管理合伙人人脉查询 - 企座",
    keywords: [
      "企业家查询",
      "投资人查询",
      "管理合伙人查询",
      "知名企业家查询"
    ],
    description: "企座人脉广场覆盖超过1亿企业家数据，方便查询您希望寻找的人脉，并支持由平台服务专员为您对接。",
    url: "renmai.aitojoy.com/list"
  };

  /**
   * 人脉广场-详情页
   * @param companyName 企业名称
   * @param personName 人脉姓名
   * @param personId 人脉ID
   * @returns SEO数据对象
   */
  static connectionDetailPage = (
    companyName: string,
    personName: string,
    personId: string
  ) => {
    return {
      title: `${companyName}${personName} 个人介绍、任职信息、企业简介、产品/服务、合作需求 - 企座`,
      keywords: [
        `${personName} 个人介绍`,
        `${personName} 任职信息`,
        `${personName} 企业简介`,
        `${personName} 产品/服务`,
        `${personName}合作需求`
      ],
      description: `了解${companyName}${personName}个人介绍、任职信息、企业简介、产品/服务、合作需求，可申请平台专员帮您对接人脉！`,
      url: `renmai.aitojoy.com/${personId}`
    };
  };

  // 产业资讯-频道页
  static industryNewsChannelPage = {
    title: "7*24小时快讯 市场聚焦 市场洞察 商讯快报 - 企座",
    keywords: [
      "7*24小时快讯",
      "市场聚焦",
      "市场洞察",
      "商讯快报"
    ],
    description: "产业资讯频道实时追踪不同产业政策新动向、市场新机会、行业新突破，助力企业把握战略先机。",
    url: "zixun.aitojoy.com"
  };

  // 产业资讯-列表页-7*24
  static industryNews24HListPage = {
    title: "7*24实时更新产业快讯 - 企座",
    keywords: [
      "行业新动态资讯",
      "产业新动向资讯"
    ],
    description: "第一时间推送行业新动态、产业新动向。",
    url: "zixun.aitojoy.com/kuaixun"
  };

  // 产业资讯-列表页-市场聚焦
  static industryNewsFocusListPage = {
    title: "行业内参 产业发展调研 - 企座",
    keywords: [
      "行业洞察内参",
      "产业发展调研"
    ],
    description: "针对近期产业发展新动向，提供深度洞察内参。",
    url: "zixun.aitojoy.com/jujiao"
  };

  // 产业资讯-列表页-市场洞察
  static industryNewsInsightListPage = {
    title: "产业新政策 国家惠企新举措 - 企座",
    keywords: [
      "国家产业布局新动向",
      "行业政策",
      "惠企政策"
    ],
    description: "聚合最新国家产业布局、政策动向信息，助您抓住先机。",
    url: "zixun.aitojoy.com/dongcha"
  };

  // 产业资讯-列表页-商讯快报
  static industryNewsBusinessListPage = {
    title: "创业项目调研 商机资讯 - 企座",
    keywords: [
      "创业项目调研",
      "最新商机资讯"
    ],
    description: "深度调研明星创业项目，足不出户一网打进商机。",
    url: "zixun.aitojoy.com/shangxunkuaibao"
  };

  /**
   * 产业资讯-详情页
   * @param channelName 资讯频道名称
   * @param newsTitle 资讯标题
   * @param newsId 资讯ID
   * @returns SEO数据对象
   */
  static industryNewsDetailPage = (
    channelName: string,
    newsTitle: string,
    newsId: string
  ) => {
    return {
      title: `${channelName} 资讯 ${newsTitle}`,
      keywords: [
        `${channelName}详细内容`
      ],
      description: `${channelName}资讯 ${newsTitle}`,
      url: `zixun.aitojoy.com/list/${newsId}`
    };
  };

  // 资金服务-频道页
  static fundingServiceChannelPage = {
    title: "找投资机构 寻上市公司 享奖补政策 金融科技服务 - 企座",
    keywords: [
      "找投资机构",
      "寻上市公司"
    ],
    description: "企座金融服务聚合红杉/IDG等超900家投资方及超过2000家有并购需求的上市公司，为创新企业提供多种获取资金渠道。",
    url: "zijinfuwu.aitojoy.com"
  };

  // 资金服务-机构列表页
  static fundingServiceListPage = {
    title: "按所在地寻找投资机构 按行业寻找投资机构 - 企座",
    keywords: [
      "按所在地寻找投资机构",
      "按行业寻找投资机构"
    ],
    description: "企座投资机构列表页，支持中小创新企业根据您的所在地及行业，快速寻找匹配您业务发展的投资机构。平台提供投资机构对接服务，留下联系方式及需求即可对接。",
    url: "zijinfuwu.aitojoy.com/list"
  };

  /**
   * 资金服务-详情页
   * @param institutionName 机构名称
   * @param institutionId 机构ID
   * @param institutionIntro 机构简介
   * @returns SEO数据对象
   */
  static fundingServiceDetailPage = (
    institutionName: string,
    institutionId: string,
    institutionIntro: string = ''
  ) => {
    return {
      title: `${institutionName}的偏好行业、机构成员、投资事件、管理基金等信息 - 企座`,
      keywords: [
        `了解${institutionName}投资偏好行业`,
        `了解${institutionName}机构成员`,
        `了解${institutionName}投资事件`,
        `了解${institutionName}管理基金`,
        `了解${institutionName}新闻舆情`
      ],
      description: institutionIntro || `${institutionName}的详细介绍`,
      url: `zijinfuwu.aitojoy.com/${institutionId}`
    };
  };

  // 企业服务-频道页
  static enterpriseServiceChannelPage = {
    title: "一站式企业服务平台_工商财税、知识产权、企业AI体检、企业实用AI智能体 - 企座",
    keywords: [
      "企业工商财税服务",
      "企业知识产权服务",
      "企业项目加速服务",
      "企业家健康出行服务",
      "企业AI体检",
      "企业实用AI智能体"
    ],
    description: "企座企业服务模块为创业公司提供工商注册、知识产权、项目加速等全生命周期服务。可通过AI大模型免费进行企业体检，同时为企业家提供健康出行服务，欢迎线上选择您需要的服务进行合作。",
    url: "qiyefuwu.aitojoy.com"
  };

  // 企业服务-列表页
  static enterpriseServiceListPage = {
    title: "企业一站式服务在线查询 订购服务 - 企座",
    keywords: [
      "企业工商财税服务在线订购",
      "企业知识产权服务在线订购",
      "企业项目加速服务在线订购",
      "企业家健康出行服务在线订购",
      "服务履约全流程在线管理"
    ],
    description: "超过30年经验的专业企业服务团队，提供超过500项企业服务。支持在线挑选与订购，帮助企业低成本高质量完成服务诉求。",
    url: "qiyefuwu.aitojoy.com/list"
  };

  /**
   * 企业服务-详情页
   * @param serviceCategory 企业服务分类
   * @param serviceName 企业服务名称
   * @param serviceId 服务ID
   * @param serviceIntro 服务优势介绍
   * @returns SEO数据对象
   */
  static enterpriseServiceDetailPage = (
    serviceCategory: string,
    serviceName: string,
    serviceId: string,
    serviceIntro: string = ''
  ) => {
    return {
      title: `${serviceCategory}精选企业服务项目${serviceName} - 企座`,
      keywords: [
        `${serviceCategory}精选服务在线订购`
      ],
      description: serviceIntro || `${serviceName}服务优势介绍`,
      url: `qiyefuwu.aitojoy.com/${serviceId}`
    };
  };

  // AI落地页
  static aiLandingPage = {
    title: "企业AI工作台 并购标的推荐 政策智能解读 项目智能解读 - 企座",
    keywords: [
      "企业AI工作台",
      "并购标的推荐",
      "政策智能解读",
      "项目智能解读"
    ],
    description: "企座为广大中小创新企业提供强大AI工作台，支持并购标的推荐、政策智能解读、项目智能解读功能，免费注册用户即可使用。",
    url: "ai.aitojoy.com"
  };

  /**
   * 项目详情页SEO数据生成
   * @param projectName 项目名称
   * @param projectId 项目ID
   * @param region 项目所属地区
   * @param industry 项目所属行业
   * @param amountRange 合作金额区间
   * @returns SEO数据对象
   */
  static projectDetailPage = (
    projectName: string,
    projectId: string,
    region: string = '',
    industry: string = '',
    amountRange: string = ''
  ) => {
    return {
      title: `${region}${industry}${amountRange}的优质项目${projectName}`,
      keywords: [
        `${projectName}详细介绍`,
        `${projectName}企业信息`,
        `${projectName}AI解读`,
        `${projectName}产业链信息`
      ],
      description: `企座为您提供${projectName}详细介绍、企业信息、AI解读、产业链信息等多维度信息`,
      url: `xiangmu.aitojoy.com/${projectId}`
    };
  };
}


