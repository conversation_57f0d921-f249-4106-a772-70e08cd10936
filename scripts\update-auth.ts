// 更新认证状态脚本
import { chromium } from '@playwright/test';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// 加载环境变量
dotenv.config();

/**
 * 更新认证状态文件
 * @param loginUrl 登录页面URL
 * @param authFilePath 认证状态保存路径
 */
async function updateAuth(loginUrl: string, authFilePath: string) {
  console.log(`开始更新认证状态文件: ${authFilePath}`);
  console.log(`登录URL: ${loginUrl}`);
  
  // 启动浏览器（非无头模式，以便可以看到登录过程）
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 100 // 放慢操作速度，便于观察
  });
  
  // 创建新的上下文
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  // 创建新的页面
  const page = await context.newPage();
  
  try {
    // 导航到登录页面
    console.log('正在导航到登录页面...');
    await page.goto(loginUrl);
    
    // 等待用户手动登录
    console.log('\n请在打开的浏览器中完成登录操作...');
    console.log('登录成功后，脚本将自动保存认证状态\n');
    
    // 等待用户登录完成
    // 这里可以根据实际情况调整等待条件
    // 例如等待特定元素出现，或者等待URL变化
    await page.waitForTimeout(60000); // 给用户60秒时间登录
    
    // 确保目录存在
    const dir = path.dirname(authFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 保存认证状态到文件
    await context.storageState({ path: authFilePath });
    
    console.log(`\n✅ 认证状态已成功保存到: ${authFilePath}`);
  } catch (error) {
    console.error('更新认证状态时出错:', error);
  } finally {
    // 关闭浏览器
    await browser.close();
  }
}

// 主函数
async function main() {
  // 检查命令行参数
  const args = process.argv.slice(2);
  const type = args[0] || 'pc'; // 默认更新PC端认证
  
  switch (type.toLowerCase()) {
    case 'pc':
      // 更新企座PC端认证
      await updateAuth(
        process.env.QIZUO_PC_URL || 'https://test-www.aitojoy.com',
        './data/qizuo-pc-auth.json'
      );
      break;
    
    case 'admin':
      // 更新后台管理认证
      await updateAuth(
        process.env.HOUTAI_URL || 'https://admin.aitojoy.com',
        './data/qizuo-admin-auth.json'
      );
      break;
    
    default:
      console.error(`未知的认证类型: ${type}`);
      console.log('可用选项: pc, admin');
      process.exit(1);
  }
}

// 执行主函数
main().catch(console.error);
