// @ts-check
import type { Page } from '@playwright/test';
import { expect } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';

export class RenMaiGuangChangPage {
  private readonly page: Page;
  private readonly ai: PlayWrightAiFixtureType['ai'];
  private readonly aiTap: PlayWrightAiFixtureType['aiTap'];
  private readonly aiInput: PlayWrightAiFixtureType['aiInput'];
  private readonly aiAssert: PlayWrightAiFixtureType['aiAssert'];
  private readonly aiQuery: PlayWrightAiFixtureType['aiQuery'];
  private readonly aiWaitFor: PlayWrightAiFixtureType['aiWaitFor'];

  constructor(page: Page, aiFixtures?: PlayWrightAiFixtureType) {
    this.page = page;
    if (aiFixtures) {
      this.ai = aiFixtures.ai;
      this.aiTap = aiFixtures.aiTap;
      this.aiInput = aiFixtures.aiInput;
      this.aiAssert = aiFixtures.aiAssert;
      this.aiQuery = aiFixtures.aiQuery;
      this.aiWaitFor = aiFixtures.aiWaitFor;
    }
  }

  /**
   * 导航到人脉广场页面
   */
  async navigateToRenMaiGuangChang() {
    await this.page.goto('https://test-www.aitojoy.com/connections/home');
    // 使用AI等待页面加载完成
    if (this.aiWaitFor) {
      await this.aiWaitFor('页面已完全加载', { timeoutMs: 5000 });
    } else {
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 验证页面标题包含预期文本
   */
  async verifyPageTitle(expectedTitle: string) {
    if (this.aiAssert) {
      // 使用AI断言验证标题
      await this.aiAssert(`页面标题包含"${expectedTitle}"`);
    } else {
      const title = await this.page.title();
      expect(title).toContain(expectedTitle);
    }
  }

  /**
   * 点击导航栏中的按钮
   */
  async clickNavButton(buttonName: string) {
    if (this.aiTap) {
      // 使用AI点击导航按钮
      await this.aiTap(`导航栏中的"${buttonName}"按钮`);
    } else {
      await this.page.getByRole('button', { name: buttonName }).click();
    }
  }

  /**
   * 搜索人脉
   */
  async searchConnection(keyword: string) {
    if (this.aiInput && this.ai) {
      // 使用AI输入搜索关键词
      await this.aiInput(keyword, '搜索框');
      // 使用AI按下回车键
      await this.ai('按下回车键');
    } else {
      await this.page.getByPlaceholder('搜索').fill(keyword);
      await this.page.keyboard.press('Enter');
    }
  }

  /**
   * 验证搜索结果包含特定文本
   */
  async verifySearchResults(expectedText: string) {
    if (this.aiAssert) {
      // 使用AI断言验证搜索结果
      await this.aiAssert(`搜索结果中包含"${expectedText}"`);
    } else {
      await expect(this.page.getByText(expectedText)).toBeVisible();
    }
  }

  /**
   * 点击筛选选项
   */
  async clickFilterOption(optionName: string) {
    if (this.aiTap) {
      // 使用AI点击筛选选项
      await this.aiTap(`筛选选项"${optionName}"`);
    } else {
      await this.page.getByText(optionName).click();
    }
  }

  /**
   * 验证人脉列表是否可见
   */
  async verifyConnectionListVisible() {
    if (this.aiAssert) {
      // 使用AI断言验证人脉列表是否可见
      await this.aiAssert('人脉列表已显示在页面上');
    } else {
      await expect(this.page.locator('.connection-list')).toBeVisible();
    }
  }
}