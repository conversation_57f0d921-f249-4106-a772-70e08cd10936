# SEO测试URL配置说明

## 概述

已将SEO测试用例中的硬编码URL修改为从环境变量读取，支持动态配置不同环境的URL。

## 修改内容

### 1. 新增URL配置对象

在 `e2e\qizuo_pc\seo-validator.spec.ts` 中新增了 `URL_CONFIG` 对象：

```typescript
const URL_CONFIG = {
  // 主站
  main: process.env.QIZUO_PC_URL || 'https://test-www.aitojoy.com',
  // 搜索
  search: process.env.QIZUO_SEARCH_URL || 'https://test-so.aitojoy.com',
  // 找项目
  project: process.env.QIZUO_PROJECT_URL || 'https://test-xiangmu.aitojoy.com',
  // 聚活动
  activity: process.env.QIZUO_ACTIVITY_URL || 'https://test-huodong.aitojoy.com',
  // 人脉广场
  connection: process.env.QIZUO_CONNECTION_URL || 'https://test-renmai.aitojoy.com',
  // 产业服务
  industryService: process.env.QIZUO_INDUSTRY_SERVICE_URL || 'https://test-chanyefuwu.aitojoy.com',
  // 惠企政策
  policy: process.env.QIZUO_POLICY_URL || 'https://test-zhengce.aitojoy.com',
  // 企业诊断
  diagnosis: process.env.QIZUO_DIAGNOSIS_URL || 'https://test-qiyezhenduan.aitojoy.com',
  // 联营服务
  jointOperation: process.env.QIZUO_JOINT_OPERATION_URL || 'https://test-lianyingfuwu.aitojoy.com',
  // 创业课堂
  course: process.env.QIZUO_COURSE_URL || 'https://test-ketang.aitojoy.com',
  // 产业资讯
  news: process.env.QIZUO_NEWS_URL || 'https://test-zixun.aitojoy.com',
  // 资金服务
  funding: process.env.QIZUO_FUNDING_URL || 'https://test-zijinfuwu.aitojoy.com',
  // 企业服务
  enterpriseService: process.env.QIZUO_ENTERPRISE_SERVICE_URL || 'https://test-qiyefuwu.aitojoy.com',
  // AI落地页
  ai: process.env.QIZUO_AI_URL || 'https://test-ai.aitojoy.com'
};
```

### 2. 替换硬编码URL

将所有测试用例中的硬编码URL替换为使用 `URL_CONFIG` 对象：

- `await page.goto('https://test-www.aitojoy.com')` → `await page.goto(URL_CONFIG.main)`
- `await page.goto('https://test-so.aitojoy.com')` → `await page.goto(URL_CONFIG.search)`
- `await page.goto('https://test-xiangmu.aitojoy.com')` → `await page.goto(URL_CONFIG.project)`
- 等等...

### 3. 支持子路径拼接

对于需要访问子路径的测试用例，使用模板字符串拼接：

- `await page.goto('https://test-huodong.aitojoy.com/list')` → `await page.goto(\`\${URL_CONFIG.activity}/list\`)`
- `await page.goto('https://test-zhengce.aitojoy.com/jiangbu')` → `await page.goto(\`\${URL_CONFIG.policy}/jiangbu\`)`

## 环境变量配置

### 1. 创建环境变量文件

复制 `.env.example` 文件为 `.env`：

```bash
cp .env.example .env
```

### 2. 修改环境变量

根据实际测试环境修改 `.env` 文件中的URL：

```env
# 测试环境
QIZUO_PC_URL=https://test-www.aitojoy.com
QIZUO_SEARCH_URL=https://test-so.aitojoy.com
# ... 其他URL

# 生产环境
# QIZUO_PC_URL=https://www.aitojoy.com
# QIZUO_SEARCH_URL=https://so.aitojoy.com
# ... 其他URL
```

## 使用方法

### 1. 默认测试环境

如果不设置环境变量，测试将使用默认的测试环境URL。

### 2. 自定义环境

设置环境变量来指定不同的测试环境：

```bash
# Windows PowerShell
$env:QIZUO_PC_URL="https://staging-www.aitojoy.com"
$env:QIZUO_SEARCH_URL="https://staging-so.aitojoy.com"

# Linux/Mac
export QIZUO_PC_URL="https://staging-www.aitojoy.com"
export QIZUO_SEARCH_URL="https://staging-so.aitojoy.com"
```

### 3. 运行测试

```bash
# 使用默认环境变量
npx playwright test e2e/qizuo_pc/seo-validator.spec.ts

# 或者在命令行中指定环境变量
QIZUO_PC_URL=https://prod-www.aitojoy.com npx playwright test e2e/qizuo_pc/seo-validator.spec.ts
```

## 优势

1. **灵活性**：可以轻松切换不同的测试环境
2. **可维护性**：统一管理所有URL配置
3. **安全性**：避免在代码中硬编码敏感URL
4. **可扩展性**：新增环境只需要修改环境变量
5. **向后兼容**：如果不设置环境变量，使用默认的测试环境URL

## 注意事项

1. 确保 `.env` 文件不要提交到版本控制系统
2. 在CI/CD环境中正确设置环境变量
3. 测试前确认环境变量中的URL是否可访问
4. 不同环境的URL格式应保持一致
